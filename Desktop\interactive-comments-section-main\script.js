// Global variables
let commentsData = null;
let currentUser = null;
let nextId = 5; // Start from 5 since we have 4 existing comments/replies

// Initialize the application
document.addEventListener('DOMContentLoaded', async function() {
    await loadData();
    renderComments();
    setupEventListeners();
});

// Load data from JSON file or localStorage
async function loadData() {
    try {
        // Try to load from localStorage first
        const savedData = localStorage.getItem('commentsData');
        if (savedData) {
            const data = JSON.parse(savedData);
            commentsData = data.comments;
            currentUser = data.currentUser;
        } else {
            // Load from JSON file
            const response = await fetch('./data.json');
            const data = await response.json();
            commentsData = data.comments;
            currentUser = data.currentUser;
            saveToLocalStorage();
        }
    } catch (error) {
        console.error('Error loading data:', error);
    }
}

// Save data to localStorage
function saveToLocalStorage() {
    const data = {
        comments: commentsData,
        currentUser: currentUser
    };
    localStorage.setItem('commentsData', JSON.stringify(data));
}

// Render all comments
function renderComments() {
    const container = document.getElementById('comments-container');
    container.innerHTML = '';
    
    // Sort comments by score (highest first)
    const sortedComments = [...commentsData].sort((a, b) => b.score - a.score);
    
    sortedComments.forEach(comment => {
        const commentElement = createCommentElement(comment);
        container.appendChild(commentElement);
        
        // Render replies if they exist
        if (comment.replies && comment.replies.length > 0) {
            const repliesContainer = document.createElement('div');
            repliesContainer.className = 'replies-container';
            
            comment.replies.forEach(reply => {
                const replyElement = createCommentElement(reply, true);
                repliesContainer.appendChild(replyElement);
            });
            
            container.appendChild(repliesContainer);
        }
    });
}

// Create a comment element
function createCommentElement(comment, isReply = false) {
    const commentDiv = document.createElement('div');
    commentDiv.className = 'comment';
    commentDiv.dataset.id = comment.id;
    
    const isCurrentUser = comment.user.username === currentUser.username;
    
    commentDiv.innerHTML = `
        <div class="vote-container">
            <button class="vote-btn" onclick="vote(${comment.id}, 'up')">
                <svg width="11" height="11" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.33 10.896c.137 0 .255-.05.354-.149.1-.1.149-.217.149-.354V7.004h3.315c.136 0 .254-.05.354-.149.099-.1.148-.217.148-.354V5.272a.483.483 0 0 0-.148-.354.483.483 0 0 0-.354-.149H6.833V1.4a.483.483 0 0 0-.149-.354.483.483 0 0 0-.354-.149H4.915a.483.483 0 0 0-.354.149c-.1.1-.149.217-.149.354v3.37H1.08a.483.483 0 0 0-.354.148c-.1.1-.149.217-.149.354V6.5c0 .137.05.255.149.354.1.099.217.149.354.149h3.333v3.39c0 .136.05.254.149.353.1.1.217.15.354.15H6.33Z" fill="#C5C6EF"/>
                </svg>
            </button>
            <span class="vote-score">${comment.score}</span>
            <button class="vote-btn" onclick="vote(${comment.id}, 'down')">
                <svg width="11" height="3" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9.256 2.66c.204 0 .38-.056.53-.167.148-.11.222-.243.222-.396V.722c0-.152-.074-.284-.223-.395a.859.859 0 0 0-.53-.167H.76a.859.859 0 0 0-.53.167C.083.437.009.57.009.722v1.375c0 .153.074.285.223.396a.859.859 0 0 0 .53.167h8.495Z" fill="#C5C6EF"/>
                </svg>
            </button>
        </div>
        <div class="comment-content">
            <div class="comment-header">
                <img src="${comment.user.image.png}" alt="${comment.user.username}" class="avatar">
                <span class="username">${comment.user.username}</span>
                ${isCurrentUser ? '<span class="you-badge">you</span>' : ''}
                <span class="timestamp">${comment.createdAt}</span>
            </div>
            <div class="comment-text" id="comment-text-${comment.id}">
                ${isReply && comment.replyingTo ? `<span class="mention">@${comment.replyingTo}</span> ` : ''}${comment.content}
            </div>
            <div class="comment-actions">
                ${isCurrentUser ? `
                    <button class="action-btn delete-btn" onclick="showDeleteModal(${comment.id})">
                        <svg width="12" height="14" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1.167 12.448c0 .854.7 1.552 1.555 1.552h6.222c.856 0 1.556-.698 1.556-1.552V3.5H1.167v8.948Zm10.5-11.281H8.75L7.773 0h-3.88l-.976 1.167H0v1.166h11.667V1.167Z" fill="#ED6368"/>
                        </svg>
                        Delete
                    </button>
                    <button class="action-btn edit-btn" onclick="editComment(${comment.id})">
                        <svg width="14" height="14" xmlns="http://www.w3.org/2000/svg">
                            <path d="M13.479 2.872 11.08.474a1.75 1.75 0 0 0-2.327-.06L.879 8.287a1.75 1.75 0 0 0-.5 1.06l-.375 3.648a.875.875 0 0 0 .875.875h.094l3.65-.333c.399-.04.773-.216 1.058-.499L13.540 5.175a1.75 1.75 0 0 0-.061-2.303Zm-2.975 2.923L8.159 3.449 9.865 1.7l2.389 2.39-1.75 1.706Z" fill="#5357B6"/>
                        </svg>
                        Edit
                    </button>
                ` : `
                    <button class="action-btn reply-btn" onclick="showReplyForm(${comment.id})">
                        <svg width="14" height="13" xmlns="http://www.w3.org/2000/svg">
                            <path d="M.227 4.316 5.04.16a.657.657 0 0 1 1.085.497v2.189c4.392.05 7.875.93 7.875 5.093 0 1.68-1.082 3.344-2.279 4.214-.373.272-.905-.07-.767-.51 1.24-3.964-.588-5.017-4.829-5.078v2.404c0 .566-.664.86-1.085.496L.227 5.31a.657.657 0 0 1 0-.993Z" fill="#5357B6"/>
                        </svg>
                        Reply
                    </button>
                `}
            </div>
        </div>
    `;
    
    return commentDiv;
}

// Setup event listeners
function setupEventListeners() {
    // Add comment form
    const addCommentForm = document.getElementById('add-comment-form');
    addCommentForm.addEventListener('submit', handleAddComment);
    
    // Modal event listeners
    document.getElementById('cancel-delete').addEventListener('click', hideDeleteModal);
    document.getElementById('confirm-delete').addEventListener('click', confirmDelete);
    
    // Close modal when clicking outside
    document.getElementById('delete-modal').addEventListener('click', function(e) {
        if (e.target === this) {
            hideDeleteModal();
        }
    });
}

// Handle adding new comment
function handleAddComment(e) {
    e.preventDefault();
    const content = document.getElementById('new-comment-content').value.trim();

    if (!content) {
        alert('Please enter a comment before submitting.');
        return;
    }

    const newComment = {
        id: nextId++,
        content: content,
        createdAt: getTimeAgo(new Date()),
        score: 0,
        user: currentUser,
        replies: []
    };

    commentsData.push(newComment);
    saveToLocalStorage();
    renderComments();

    // Clear form
    document.getElementById('new-comment-content').value = '';
}

// Get time ago string
function getTimeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    if (diffInSeconds < 2419200) return `${Math.floor(diffInSeconds / 604800)} weeks ago`;
    return `${Math.floor(diffInSeconds / 2419200)} months ago`;
}

// Voting functionality
function vote(commentId, direction) {
    const comment = findCommentById(commentId);
    if (!comment) return;
    
    if (direction === 'up') {
        comment.score++;
    } else if (direction === 'down') {
        comment.score--;
    }
    
    saveToLocalStorage();
    renderComments();
}

// Find comment by ID (searches both comments and replies)
function findCommentById(id) {
    for (let comment of commentsData) {
        if (comment.id === id) return comment;
        
        if (comment.replies) {
            for (let reply of comment.replies) {
                if (reply.id === id) return reply;
            }
        }
    }
    return null;
}

// Show reply form
function showReplyForm(commentId) {
    // Remove any existing reply forms
    const existingForms = document.querySelectorAll('.reply-form');
    existingForms.forEach(form => form.remove());
    
    const comment = document.querySelector(`[data-id="${commentId}"]`);
    const replyForm = document.createElement('div');
    replyForm.className = 'reply-form comment-form';
    replyForm.innerHTML = `
        <img src="${currentUser.image.png}" alt="${currentUser.username}" class="avatar">
        <textarea placeholder="Add a reply..." rows="3" required></textarea>
        <button type="button" class="btn btn-primary" onclick="submitReply(${commentId}, this)">REPLY</button>
    `;
    
    comment.insertAdjacentElement('afterend', replyForm);
    replyForm.querySelector('textarea').focus();
}

// Submit reply
function submitReply(parentId, button) {
    const form = button.closest('.reply-form');
    const content = form.querySelector('textarea').value.trim();

    if (!content) {
        alert('Please enter a reply before submitting.');
        return;
    }

    const parentComment = findCommentById(parentId);
    const replyingTo = parentComment.user.username;

    const newReply = {
        id: nextId++,
        content: content,
        createdAt: getTimeAgo(new Date()),
        score: 0,
        replyingTo: replyingTo,
        user: currentUser
    };

    // Find the parent comment in the main array and add reply
    const mainComment = commentsData.find(c => c.id === parentId || c.replies?.some(r => r.id === parentId));
    if (mainComment) {
        if (!mainComment.replies) mainComment.replies = [];
        mainComment.replies.push(newReply);
    }

    saveToLocalStorage();
    renderComments();
    form.remove();
}

// Edit comment
function editComment(commentId) {
    const comment = findCommentById(commentId);
    const commentTextElement = document.getElementById(`comment-text-${commentId}`);
    
    const originalContent = comment.content;
    const textarea = document.createElement('textarea');
    textarea.value = originalContent;
    textarea.className = 'edit-textarea';
    textarea.style.width = '100%';
    textarea.style.minHeight = '96px';
    textarea.style.border = '1px solid var(--grey-100)';
    textarea.style.borderRadius = 'var(--border-radius)';
    textarea.style.padding = 'var(--spacing-sm)';
    textarea.style.fontFamily = 'var(--font-family)';
    textarea.style.fontSize = 'var(--font-size-base)';
    textarea.style.marginBottom = 'var(--spacing-sm)';
    
    const updateButton = document.createElement('button');
    updateButton.textContent = 'UPDATE';
    updateButton.className = 'btn btn-primary';
    updateButton.style.marginLeft = 'auto';
    updateButton.style.display = 'block';
    
    commentTextElement.innerHTML = '';
    commentTextElement.appendChild(textarea);
    commentTextElement.appendChild(updateButton);
    
    updateButton.addEventListener('click', function() {
        const newContent = textarea.value.trim();
        if (newContent && newContent !== originalContent) {
            comment.content = newContent;
            saveToLocalStorage();
            renderComments();
        } else {
            renderComments(); // Revert changes
        }
    });
    
    textarea.focus();
}

// Delete modal functions
let commentToDelete = null;

function showDeleteModal(commentId) {
    commentToDelete = commentId;
    document.getElementById('delete-modal').style.display = 'flex';
}

function hideDeleteModal() {
    commentToDelete = null;
    document.getElementById('delete-modal').style.display = 'none';
}

function confirmDelete() {
    if (!commentToDelete) return;
    
    // Find and remove the comment
    for (let i = 0; i < commentsData.length; i++) {
        if (commentsData[i].id === commentToDelete) {
            commentsData.splice(i, 1);
            break;
        }
        
        // Check replies
        if (commentsData[i].replies) {
            const replyIndex = commentsData[i].replies.findIndex(r => r.id === commentToDelete);
            if (replyIndex !== -1) {
                commentsData[i].replies.splice(replyIndex, 1);
                break;
            }
        }
    }
    
    saveToLocalStorage();
    renderComments();
    hideDeleteModal();
}
