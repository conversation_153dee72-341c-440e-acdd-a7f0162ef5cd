# Front-end Style Guide

## Layout

The designs were created to the following widths:

- Mobile: 375px
- Desktop: 1440px

> 💡 These are just the design sizes. Ensure content is responsive and meets WCAG requirements by testing the full range of screen sizes from 320px to large screens.

## Colors

### Primary

- Purple 600: hsl(238, 40%, 52%)
- Pink 400: hsl(358, 79%, 66%)
- Purple 200: hsl(239, 57%, 85%)
- Pink 200: hsl(357, 100%, 86%)

### Neutral

- Grey 800: hsl(212, 24%, 26%)
- Grey 500: hsl(211, 10%, 45%)
- Grey 100: hsl(223, 19%, 93%)
- Grey 50: hsl(228, 33%, 97%)
- White: hsl(0, 100%, 100%)

## Typography

### Body Copy

- Font size (paragraph): 16px

### Font

- Family: [Rubik](https://fonts.google.com/specimen/Rubik)
- Weights: 400, 500, 700

> 💎 [Upgrade to Pro](https://www.frontendmentor.io/pro?ref=style-guide) for design file access to see all design details and get hands-on experience using a professional workflow with tools like Figma.
